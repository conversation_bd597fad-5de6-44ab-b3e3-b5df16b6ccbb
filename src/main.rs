use eterna::utils_constants::*;

fn main() {
    println!("=== Eterna Constants Demo ===\n");

    // Basic constants
    println!("Basic Constants:");
    println!("  PICK_AN_OPTION: {}", PICK_AN_OPTION);
    println!("  MAX_KEY_LENGTH: {}", MAX_KEY_LENGTH);
    println!("  MIN_LOG_SIZE: {} bytes ({} MB)", MIN_LOG_SIZE, MIN_LOG_SIZE / (1024 * 1024));
    println!("  CACHE_LIFE_SPAN: {} seconds", CACHE_LIFE_SPAN);
    println!();

    // Configuration structs
    println!("Configuration:");
    println!("  Logical Operators Default: {}", LOGICAL_OPERATORS.default);
    println!("  Limits Default: {}", LIMITS.default);
    println!("  Limits Values: {:?}", LIMITS.values);
    println!("  Timeouts - Tor: {}s, Fetcher: {}s", TIMEOUTS.is_tor, TIMEOUTS.fetcher);
    println!();

    // Arrays
    println!("Arrays:");
    println!("  Seasons: {:?}", SEASONS_LIST);
    println!("  First 3 Months: {:?}", &MONTHS_LIST[0..3]);
    println!();

    // HTTP Headers
    println!("HTTP Headers (sample):");
    for (key, value) in HTTP_HEADERS.iter().take(3) {
        println!("  {}: {}", key, value);
    }
    println!("  ... and {} more headers", HTTP_HEADERS.len() - 3);
    println!();

    // Binary paths
    println!("Binary Paths:");
    println!("  nslookup: {:?}", BINARY_PATHS.nslookup);
    println!("  pgrep: {:?}", BINARY_PATHS.pgrep);
    println!("  service: {:?}", BINARY_PATHS.service);
    println!("  sudo: {:?}", BINARY_PATHS.sudo);
    println!();

    println!("=== Demo Complete ===");
}
