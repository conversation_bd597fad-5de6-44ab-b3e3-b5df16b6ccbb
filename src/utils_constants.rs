use lazy_static::lazy_static;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::path::PathBuf;
use which::which;

// Simple string constants
pub const PICK_AN_OPTION: &str = "Pick an Option";

/// To avoid Unicode decode errors
/// NOTE: using encoding='ISO-8859-1' will mess up Persian/Arabic characters
pub const ACTION_ON_ERROR: &str = "ignore";

pub const MAX_KEY_LENGTH: usize = 20;

/// 20MB in bytes
pub const MIN_LOG_SIZE: usize = 1024 * 1024 * 20;

pub const PRINT_ENDPOINT: &str = "\r";

/// 30 minutes in seconds (1800)
pub const CACHE_LIFE_SPAN: u32 = 30 * 60;

/// LRU cache max size (default is 128)
pub const LRU_CACHE_MAXSIZE: usize = 500;

pub const ON_TRUE: [&str; 2] = ["on", "true"];

// Configuration structs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogicalOperators {
    pub values: Vec<String>,
    pub default: String,
    pub for_field: String,
}

impl Default for LogicalOperators {
    fn default() -> Self {
        Self {
            values: vec!["AND".to_string(), "OR".to_string()],
            default: "AND".to_string(),
            for_field: "OR".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchSigns {
    pub asterisk: String,
    pub caret: String,
    pub dollar: String,
    pub field_separator: String,
}

impl Default for SearchSigns {
    fn default() -> Self {
        Self {
            asterisk: "*".to_string(),
            caret: "^".to_string(),
            dollar: "$".to_string(),
            field_separator: "||".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Limits {
    pub values: Vec<u32>,
    pub default: u32,
}

impl Default for Limits {
    fn default() -> Self {
        Self {
            values: vec![25, 50, 100, 200, 500],
            default: 25,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TopsToShow {
    pub values: Vec<u32>,
    pub default: u32,
}

impl Default for TopsToShow {
    fn default() -> Self {
        Self {
            values: vec![5, 10, 15, 20],
            default: 10,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Refreshes {
    pub values: Vec<u32>,
    pub default: u32,
    pub min_nonzero: u32,
}

impl Default for Refreshes {
    fn default() -> Self {
        Self {
            values: vec![0, 5, 10, 30, 45, 60],
            default: 0,
            min_nonzero: 5,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LastLines {
    pub values: Vec<u32>,
    pub default: u32,
    pub max: u32,
}

impl Default for LastLines {
    fn default() -> Self {
        Self {
            values: vec![10, 20, 50, 100, 200],
            default: 20,
            max: 200,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentsToShow {
    pub values: Vec<String>,
    pub default: String,
}

impl Default for RecentsToShow {
    fn default() -> Self {
        Self {
            values: vec![
                "Week".to_string(),
                "2 Weeks".to_string(),
                "3 Weeks".to_string(),
                "Month".to_string(),
                "2 Months".to_string(),
                "3 Months".to_string(),
                "6 Months".to_string(),
                "9 Months".to_string(),
                "Year".to_string(),
            ],
            default: "2 Weeks".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Timeouts {
    pub is_tor: u32,
    pub fetcher: u32,
}

impl Default for Timeouts {
    fn default() -> Self {
        Self {
            is_tor: 5,
            // Setting to 2 caused constant ConnectTimeout errors.
            // 5 seems to be a proper value.
            fetcher: 5,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaxTries {
    /// High number because we are using async
    pub dl: u32,
    pub restart: u32,
    pub is_tor: u32,
}

impl Default for MaxTries {
    fn default() -> Self {
        Self {
            dl: 10,
            restart: 10,
            is_tor: 5,
        }
    }
}

// Binary paths structure
#[derive(Debug, Clone)]
pub struct BinaryPaths {
    pub nslookup: Option<PathBuf>,
    pub pgrep: Option<PathBuf>,
    pub service: Option<PathBuf>,
    pub sudo: Option<PathBuf>,
}

impl BinaryPaths {
    fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let shell_path = env::var("PATH").unwrap_or_default();
        let usr_local_bin = "/usr/local/bin";
        
        // Check if we need to add /usr/local/bin to PATH
        let debug_mode = is_debug_mode();
        let path_to_use = if !shell_path.is_empty() 
            && !debug_mode 
            && !shell_path.split(':').any(|p| p == usr_local_bin) {
            format!("{}:{}", shell_path, usr_local_bin)
        } else {
            shell_path
        };

        let binary_paths = Self {
            nslookup: which_with_path("nslookup", &path_to_use),
            pgrep: which_with_path("pgrep", &path_to_use),
            service: which_with_path("service", &path_to_use),
            sudo: which_with_path("sudo", &path_to_use),
        };

        // Pre-check for missing binaries as a runtime safeguard
        // Only check for 'service' binary in production mode
        if binary_paths.service.is_none() && !debug_mode {
            eprintln!("Warning: Required binary 'service' not found in system PATH.");
            // In a real application, you might want to return an error here
            // For this demo, we'll just warn and continue
        }

        Ok(binary_paths)
    }
}

// Helper function to check for binaries with custom PATH
fn which_with_path(binary: &str, path: &str) -> Option<PathBuf> {
    // Temporarily set PATH for this lookup
    let original_path = env::var("PATH").ok();
    env::set_var("PATH", path);
    
    let result = which(binary).ok();
    
    // Restore original PATH
    if let Some(orig) = original_path {
        env::set_var("PATH", orig);
    }
    
    result
}

// Helper function to determine debug mode
// In a real application, this would read from config file or environment
fn is_debug_mode() -> bool {
    env::var("DEBUG")
        .unwrap_or_default()
        .to_lowercase()
        .parse::<bool>()
        .unwrap_or(false)
}

// Constants arrays
pub const SEASONS_LIST: [&str; 4] = ["Spring", "Summer", "Fall", "Winter"];

pub const MONTHS_LIST: [&str; 12] = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December",
];

// HTTP headers
lazy_static! {
    pub static ref HTTP_HEADERS: HashMap<&'static str, &'static str> = {
        let mut headers = HashMap::new();
        headers.insert("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.insert("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8");
        headers.insert("Accept-Language", "en-US,en;q=0.9");
        headers.insert("Accept-Encoding", "gzip, deflate, br");
        headers.insert("DNT", "1"); // Do Not Track
        headers.insert("Connection", "keep-alive"); // using 'close' can slow down scraping or mark you as unusual
        headers.insert("Upgrade-Insecure-Requests", "1"); // sent by browsers when accessing HTTPS URLs
        headers
    };
    
    pub static ref LIVE_MONITOR_DB_HEADERS: Vec<&'static str> = vec!["", "Message"];
    
    // Global configuration instances
    pub static ref LOGICAL_OPERATORS: LogicalOperators = LogicalOperators::default();
    pub static ref SEARCH_SIGNS: SearchSigns = SearchSigns::default();
    pub static ref LIMITS: Limits = Limits::default();
    pub static ref TOPS_TO_SHOW: TopsToShow = TopsToShow::default();
    pub static ref REFRESHES: Refreshes = Refreshes::default();
    pub static ref LAST_LINES: LastLines = LastLines::default();
    pub static ref RECENTS_TO_SHOW: RecentsToShow = RecentsToShow::default();
    pub static ref TIMEOUTS: Timeouts = Timeouts::default();
    pub static ref MAX_TRIES: MaxTries = MaxTries::default();
    
    pub static ref BINARY_PATHS: BinaryPaths = BinaryPaths::new()
        .expect("Failed to initialize binary paths");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_constants() {
        assert_eq!(PICK_AN_OPTION, "Pick an Option");
        assert_eq!(MAX_KEY_LENGTH, 20);
        assert_eq!(MIN_LOG_SIZE, 20 * 1024 * 1024);
        assert_eq!(CACHE_LIFE_SPAN, 1800);
    }

    #[test]
    fn test_logical_operators() {
        assert_eq!(LOGICAL_OPERATORS.default, "AND");
        assert_eq!(LOGICAL_OPERATORS.for_field, "OR");
        assert!(LOGICAL_OPERATORS.values.contains(&"AND".to_string()));
        assert!(LOGICAL_OPERATORS.values.contains(&"OR".to_string()));
    }

    #[test]
    fn test_seasons_and_months() {
        assert_eq!(SEASONS_LIST.len(), 4);
        assert_eq!(MONTHS_LIST.len(), 12);
        assert_eq!(SEASONS_LIST[0], "Spring");
        assert_eq!(MONTHS_LIST[0], "January");
    }

    #[test]
    fn test_http_headers() {
        assert!(HTTP_HEADERS.contains_key("User-Agent"));
        assert!(HTTP_HEADERS.contains_key("Accept"));
        assert_eq!(HTTP_HEADERS.get("DNT"), Some(&"1"));
    }
}
