use eterna::utils_constants::*;
use std::time::Instant;

fn main() {
    println!("=== Performance Benchmark ===\n");

    // Benchmark constant access
    let start = Instant::now();
    for _ in 0..1_000_000 {
        let _ = PICK_AN_OPTION;
        let _ = MAX_KEY_LENGTH;
        let _ = CACHE_LIFE_SPAN;
    }
    let duration = start.elapsed();
    println!("1M constant accesses: {:?}", duration);

    // Benchmark struct access
    let start = Instant::now();
    for _ in 0..1_000_000 {
        let _ = LIMITS.default;
        let _ = &LIMITS.values;
        let _ = TIMEOUTS.fetcher;
    }
    let duration = start.elapsed();
    println!("1M struct accesses: {:?}", duration);

    // Benchmark array access
    let start = Instant::now();
    for _ in 0..1_000_000 {
        let _ = SEASONS_LIST[0];
        let _ = MONTHS_LIST[5];
    }
    let duration = start.elapsed();
    println!("1M array accesses: {:?}", duration);

    // Benchmark HashMap access
    let start = Instant::now();
    for _ in 0..100_000 {
        let _ = HTTP_HEADERS.get("User-Agent");
        let _ = HTTP_HEADERS.get("Accept");
    }
    let duration = start.elapsed();
    println!("100K HashMap accesses: {:?}", duration);

    println!("\n=== Memory Usage ===");
    println!("HTTP_HEADERS size: {} entries", HTTP_HEADERS.len());
    println!("SEASONS_LIST size: {} entries", SEASONS_LIST.len());
    println!("MONTHS_LIST size: {} entries", MONTHS_LIST.len());
    println!("LIMITS values: {} entries", LIMITS.values.len());

    println!("\n=== Benchmark Complete ===");
}
