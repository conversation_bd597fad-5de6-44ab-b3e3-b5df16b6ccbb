# Eterna Constants - Rust Conversion

This is a Rust conversion of the Python `base/utils_constants.py` file from the Eterna Django project.

## Overview

The original Python file contained various constants and configuration values used throughout the Django application. This Rust version preserves all the functionality while making it more type-safe and efficient.

## Key Features

### Constants Converted
- **Basic Constants**: String and numeric constants like `PICK_AN_OPTION`, `MAX_KEY_LENGTH`, etc.
- **Configuration Structs**: Structured data like `LogicalOperators`, `SearchSigns`, `Limits`, etc.
- **Arrays**: Static arrays for seasons and months
- **HTTP Headers**: HashMap of HTTP headers for web scraping
- **Binary Paths**: System binary path detection with environment-aware logic

### Rust Improvements
1. **Type Safety**: All constants are properly typed
2. **Memory Efficiency**: Uses static arrays and lazy initialization
3. **Error Handling**: Proper error handling for binary path detection
4. **Serialization**: All structs support serde serialization
5. **Testing**: Comprehensive unit tests included

## Usage

```rust
use eterna::utils_constants::*;

// Access basic constants
println!("Cache lifespan: {} seconds", CACHE_LIFE_SPAN);

// Use configuration structs
println!("Default limit: {}", LIMITS.default);
println!("Available limits: {:?}", LIMITS.values);

// Access HTTP headers
for (key, value) in HTTP_HEADERS.iter() {
    println!("{}: {}", key, value);
}

// Check binary paths
if let Some(sudo_path) = &BINARY_PATHS.sudo {
    println!("sudo found at: {:?}", sudo_path);
}
```

## Building and Running

```bash
# Check compilation
cargo check

# Run tests
cargo test

# Run the demo
cargo run

# Build for release
cargo build --release
```

## Dependencies

- `serde`: For serialization support
- `which`: For finding system binaries
- `lazy_static`: For lazy initialization of static values

## Environment Variables

- `DEBUG`: Set to "true" for development mode (affects binary path checking)
- `PATH`: Used for finding system binaries

## Differences from Python Version

1. **No Django Dependency**: Removed Django settings dependency
2. **Environment-based Debug**: Uses `DEBUG` environment variable instead of Django settings
3. **Static Typing**: All values are statically typed
4. **Lazy Initialization**: Complex values are lazily initialized for better performance
5. **Error Handling**: More robust error handling for system operations

## Testing

The module includes comprehensive tests covering:
- Basic constant values
- Configuration struct defaults
- Array contents
- HTTP header presence
- Binary path detection logic

Run tests with: `cargo test`

## Performance

The Rust version offers several performance benefits:
- Zero-cost abstractions
- Compile-time optimizations
- Efficient memory usage with static allocations
- Lazy initialization of expensive operations

## Migration Notes

When migrating from the Python version:
1. Replace `settings.DEBUG` checks with `is_debug_mode()` function
2. Use struct field access instead of `SimpleNamespace` attribute access
3. Handle `Option<PathBuf>` for binary paths instead of `None` strings
4. Use static references (`&LIMITS`) instead of importing objects
